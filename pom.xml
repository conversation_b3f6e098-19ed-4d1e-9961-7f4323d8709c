<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.5.12</version>
    </parent>

    <groupId>com.tigo.galaxion</groupId>
    <artifactId>tigo-sales-facade</artifactId>
    <version>1.0.2-SNAPSHOT-10397</version>
    <name>${project.artifactId}</name>

    <properties>
        <!--====================================================================
         = ITSF LIB
         ==================================================================== -->
        <itsf-keycloak-spring.version>2.2.0</itsf-keycloak-spring.version>
        
        <java.version>17</java.version>

        <itsf-logging-spring.version>2.1.3</itsf-logging-spring.version>
        <sepa-lib.version>1.0.0</sepa-lib.version>
        <galaxion-context-header.version>1.0.1</galaxion-context-header.version>

        <zalando-problem.version>0.27.0</zalando-problem.version>
        <springfox-swagger.version>3.0.0</springfox-swagger.version>
        <feign.version>11.9.1</feign.version>
        <spring-cloud-starter-openfeign.version>3.1.9</spring-cloud-starter-openfeign.version>
        <google-guava.version>31.1-android</google-guava.version>
        <spring-security-oauth2-client.version>5.7.1</spring-security-oauth2-client.version>

        <spring-boot-admin-client.version>2.5.6</spring-boot-admin-client.version>
        <zipkin.version>2.2.8.RELEASE</zipkin.version>
        <jacoco-maven-plugin.version>0.8.7</jacoco-maven-plugin.version>
        <jib-maven-plugin.version>3.1.4</jib-maven-plugin.version>
        <itsf-spring-pagination.version>2.0.0</itsf-spring-pagination.version>
        <itsf-validation.version>1.0.1</itsf-validation.version>
        <apache-commons.version>3.12.0</apache-commons.version>
        <docker.image.from>galaxion-java-${java.version}-liquibase</docker.image.from>
    </properties>

    <dependencies>
        <!--================================================================
        = SPRING
        =================================================================-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web-services</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>

        <!--================================================================
        = GOOGLE
        =================================================================-->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${google-guava.version}</version>
            <scope>test</scope>
        </dependency>

        <!--================================================================
        = ITSF
        =================================================================-->
        <dependency>
            <groupId>fr.itsf.lib</groupId>
            <artifactId>itsf-logging-spring-boot-starter</artifactId>
            <version>${itsf-logging-spring.version}</version>
        </dependency>
        <dependency>
            <groupId>fr.itsf.lib</groupId>
            <artifactId>itsf-keycloak-spring-boot-starter</artifactId>
            <version>${itsf-keycloak-spring.version}</version>
        </dependency>
        <dependency>
            <groupId>fr.itsf.lib</groupId>
            <artifactId>itsf-spring-pagination</artifactId>
            <version>${itsf-spring-pagination.version}</version>
        </dependency>

        <!-- Galaxion context header spring boot starter module -->
        <dependency>
            <groupId>fr.njj.galaxion.lib</groupId>
            <artifactId>galaxion-context-header-spring-boot-starter</artifactId>
            <version>${galaxion-context-header.version}</version>
        </dependency>


        <!--================================================================
        = ZALANDO PROBLEM
        =================================================================-->
        <dependency>
            <groupId>org.zalando</groupId>
            <artifactId>problem-spring-web</artifactId>
            <version>${zalando-problem.version}</version>
        </dependency>

        <!--================================================================
        = SECURITY
        =================================================================-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-client</artifactId>
            <version>${spring-security-oauth2-client.version}</version>
        </dependency>
        <dependency>
            <groupId>org.activiti.cloud</groupId>
            <artifactId>activiti-cloud-services-common-security-keycloak</artifactId>
            <version>7.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-security</artifactId>
            <version>2.2.5.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security.oauth</groupId>
            <artifactId>spring-security-oauth2</artifactId>
            <version>2.5.2.RELEASE</version>
        </dependency>


        <!--====================================================================
        = SWAGGER
        =====================================================================-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>${springfox-swagger.version}</version>
        </dependency>

        <!--================================================================
        = DATABASE
        =================================================================-->
        <dependency>
            <groupId>org.mariadb.jdbc</groupId>
            <artifactId>mariadb-java-client</artifactId>
            <version>${mariadb.version}</version>
        </dependency>

        <!--================================================================
        = TESTING
        =================================================================-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <!--================================================================
        = Feign
        =================================================================-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>${spring-cloud-starter-openfeign.version}</version>
        </dependency>
        <dependency>
            <!-- Required to use PATCH -->
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
            <version>${feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.java-json-tools</groupId>
            <artifactId>json-patch</artifactId>
            <version>1.13</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>

        <!--====================================================================
        = TRACING
        =====================================================================-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-zipkin</artifactId>
            <version>${zipkin.version}</version>
        </dependency>

        <!--================================================================
        = SPRING ADMIN
        =================================================================-->
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot-admin-client.version}</version>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <!--====================================================================
        = STATIC METAMODEL GENERATION
        =====================================================================-->
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-jpamodelgen</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.34</version>
        </dependency>

        <!--================================================================
        = CLOUD STREAM
        =================================================================-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-rabbit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-stream</artifactId>
        </dependency>

        <!--================================================================
        = APACHE
        =================================================================-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${apache-commons.version}</version>
        </dependency>


        <!--================================================================
        = END
        =================================================================-->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-jackson</artifactId>
            <version>9.5.1</version>
        </dependency>

        <dependency>
            <groupId>mc.monacotelecom.workflowengine</groupId>
            <artifactId>workflow-engine-app-dto</artifactId>
            <version>3.1.11</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2020.0.4</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>build-info</id>
                        <goals>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-maven-plugin</artifactId>
                <configuration>
                    <propertyFile>src\main\resources\liquibase.properties</propertyFile>
                </configuration>
            </plugin>
            <!--<plugin>
                <groupId>org.jvnet.jaxb2.maven2</groupId>
                <artifactId>maven-jaxb2-plugin</artifactId>
                <version>0.15.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <schemaLanguage>WSDL</schemaLanguage>
                    <generateDirectory>${project.basedir}/src/main/java</generateDirectory>
                    <generatePackage>com.tigo.galaxion.sales.facade.soap.multiple_operations</generatePackage>
                    <schemaDirectory>${project.basedir}/src/main/resources/wsdl</schemaDirectory>
                    <schemaIncludes>
                        <include>multipleOperations_1.wsdl</include>
                    </schemaIncludes>
                    <args>
                        <arg>-wsdl</arg>
                    </args>
                </configuration>
            </plugin>-->
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>gitlab-ci</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <version>0.8.7</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>prepare-agent</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>report</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>report</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>3.1.4</version>
                        <configuration>
                            <from>
                                <image>{auto}/${docker.image.from}:latest</image>
                            </from>
                            <to>
                                <image>{auto}</image>
                                <tags>
                                    <tag>${project.version}</tag>
                                </tags>
                            </to>
                            <extraDirectories>
                                <paths>
                                    <path>
                                        <from>src/main/resources</from>
                                        <into>/liquibase/changelog</into>
                                    </path>
                                </paths>
                            </extraDirectories>
                            <container>
                                <user>tomcat</user>
                            </container>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>